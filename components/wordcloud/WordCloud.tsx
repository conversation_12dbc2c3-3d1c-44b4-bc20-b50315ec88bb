import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'

interface PostInfo {
  id: number
  title: string
  link?: string
  author?: string
  time?: string
  xpath?: string
}

interface WordCloudItem {
  text: string
  size: number
  relatedPosts?: PostInfo[]
}

interface WordCloudComponentProps {
  words: WordCloudItem[]
  width?: number
  height?: number
  minFontSize?: number
  maxFontSize?: number
  onWordHover?: (word: string | null, posts?: PostInfo[], event?: MouseEvent) => void
  onWordClick?: (word: string, posts?: PostInfo[]) => void
}

interface WordPosition {
  x: number
  y: number
  width: number
  height: number
  word: WordCloudItem
  fontSize: number
  color: string
  rotation: number
}

const WordCloudComponent: React.FC<WordCloudComponentProps> = ({
  words,
  width = 400,
  height = 300,
  minFontSize = 12,
  maxFontSize = 48,
  onWordHover,
  onWordClick
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [wordPositions, setWordPositions] = useState<WordPosition[]>([])
  const [hoveredWord, setHoveredWord] = useState<string | null>(null)

  // 颜色数组 - 使用useRef避免每次渲染重新创建
  const colors = useRef(['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16']).current

  // 检查两个矩形是否重叠
  const isOverlapping = (rect1: { x: number; y: number; width: number; height: number }, rect2: { x: number; y: number; width: number; height: number }): boolean => {
    return !(rect1.x + rect1.width < rect2.x || 
             rect2.x + rect2.width < rect1.x || 
             rect1.y + rect1.height < rect2.y || 
             rect2.y + rect2.height < rect1.y)
  }

  // 生成词云布局
  const generateLayout = useCallback(() => {
    if (!words.length) {
      return []
    }

    const positions: WordPosition[] = []
    
    // 创建临时canvas用于文本测量
    const tempCanvas = document.createElement('canvas')
    const ctx = tempCanvas.getContext('2d')
    if (!ctx) return []

    // 按大小排序，大的词优先放置
    const sortedWords = [...words].sort((a, b) => b.size - a.size)

    sortedWords.forEach((word, index) => {
      // 计算字体大小，将word.size映射到指定的字体大小范围
      // 首先找到所有词的size范围
      const minSize = Math.min(...words.map(w => w.size))
      const maxSize = Math.max(...words.map(w => w.size))
      
      // 将word.size从[minSize, maxSize]映射到[minFontSize, maxFontSize]
      let fontSize: number
      if (maxSize === minSize) {
        // 如果所有词的size相同，使用中等字体大小
        fontSize = (minFontSize + maxFontSize) / 2
      } else {
        // 线性映射
        const ratio = (word.size - minSize) / (maxSize - minSize)
        fontSize = minFontSize + ratio * (maxFontSize - minFontSize)
      }
      fontSize = Math.round(fontSize) // 四舍五入到整数
      const color = colors[index % colors.length]
      // 使用固定的随机种子避免每次重新生成随机旋转
      const rotation = ((word.text.charCodeAt(0) + word.size) % 13 - 6) * 5 // -30到30度的固定旋转

      // 设置字体以测量文本尺寸
      ctx.font = `bold ${fontSize}px sans-serif`
      const textMetrics = ctx.measureText(word.text)
      const textWidth = textMetrics.width
      const textHeight = fontSize

      // 尝试找到不重叠的位置
      let placed = false
      let attempts = 0
      const maxAttempts = 100
      
      // 使用确定性的种子来保证位置稳定
      const seed = word.text.charCodeAt(0) + word.size

      while (!placed && attempts < maxAttempts) {
        // 螺旋式搜索位置，使用确定性算法
        const angle = (attempts * 0.5) + (seed * 0.1)
        const radius = attempts * 2
        const centerX = width / 2
        const centerY = height / 2
        
        const x = centerX + Math.cos(angle) * radius - textWidth / 2
        const y = centerY + Math.sin(angle) * radius - textHeight / 2

        // 检查边界
        if (x >= 0 && y >= 0 && x + textWidth <= width && y + textHeight <= height) {
          const newRect = { x, y, width: textWidth, height: textHeight }
          
          // 检查是否与已放置的词重叠
          const overlapping = positions.some(pos => 
            isOverlapping(newRect, { x: pos.x, y: pos.y, width: pos.width, height: pos.height })
          )

          if (!overlapping) {
            positions.push({
              x,
              y,
              width: textWidth,
              height: textHeight,
              word,
              fontSize,
              color,
              rotation
            })
            placed = true
          }
        }
        attempts++
      }

      // 如果无法找到位置，使用确定性的备用位置
      if (!placed) {
        const fallbackX = (seed * 37) % (width - textWidth)
        const fallbackY = (seed * 73) % (height - textHeight)
        positions.push({
          x: Math.max(0, fallbackX),
          y: Math.max(0, fallbackY),
          width: textWidth,
          height: textHeight,
          word,
          fontSize,
          color,
          rotation
        })
      }
    })

    return positions
  }, [words, width, height])

  // 绘制词云
  const drawWordCloud = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) {
      console.log('Canvas not found')
      return
    }

    const ctx = canvas.getContext('2d')
    if (!ctx) {
      console.log('Canvas context not found')
      return
    }
    
    // 清空画布
    ctx.clearRect(0, 0, width, height)

    // 绘制每个词
    wordPositions.forEach((pos, index) => {
      ctx.save()
      
      // 设置字体和颜色
      ctx.font = `bold ${pos.fontSize}px sans-serif`
      ctx.fillStyle = hoveredWord === pos.word.text ? '#ff6b6b' : pos.color
      ctx.textAlign = 'left'
      ctx.textBaseline = 'top'

      // 应用旋转
      if (pos.rotation !== 0) {
        ctx.translate(pos.x + pos.width / 2, pos.y + pos.height / 2)
        ctx.rotate(pos.rotation * Math.PI / 180)
        ctx.fillText(pos.word.text, -pos.width / 2, -pos.height / 2)
      } else {
        ctx.fillText(pos.word.text, pos.x, pos.y)
      }
      
      ctx.restore()
    })
  }, [wordPositions, hoveredWord, width, height])

  // 处理鼠标事件
  const handleMouseMove = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // 查找鼠标位置下的词
    const hoveredPosition = wordPositions.find(pos => 
      x >= pos.x && x <= pos.x + pos.width && 
      y >= pos.y && y <= pos.y + pos.height
    )

    if (hoveredPosition) {
      if (hoveredWord !== hoveredPosition.word.text) {
        setHoveredWord(hoveredPosition.word.text)
        onWordHover?.(hoveredPosition.word.text, hoveredPosition.word.relatedPosts, event.nativeEvent)
      }
      canvas.style.cursor = 'pointer'
    } else {
      if (hoveredWord) {
        setHoveredWord(null)
        onWordHover?.(null)
      }
      canvas.style.cursor = 'default'
    }
  }, [wordPositions, hoveredWord, onWordHover])

  const handleMouseLeave = useCallback(() => {
    setHoveredWord(null)
    onWordHover?.(null)
    if (canvasRef.current) {
      canvasRef.current.style.cursor = 'default'
    }
  }, [onWordHover])

  const handleClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // 查找点击位置下的词
    const clickedPosition = wordPositions.find(pos => 
      x >= pos.x && x <= pos.x + pos.width && 
      y >= pos.y && y <= pos.y + pos.height
    )

    if (clickedPosition) {
      // 阻止事件冒泡，避免触发面板的点击处理器
      event.preventDefault()
      event.stopPropagation()
      onWordClick?.(clickedPosition.word.text, clickedPosition.word.relatedPosts)
    }
  }, [wordPositions, onWordClick])

  // 使用useMemo缓存布局计算结果
  const memoizedPositions = useMemo(() => {
    return generateLayout()
  }, [words, width, height, generateLayout])

  // 初始化和更新布局
  useEffect(() => {
    setWordPositions(memoizedPositions)
  }, [memoizedPositions])

  // 绘制词云
  useEffect(() => {
    drawWordCloud()
  }, [drawWordCloud])

  return (
    <canvas
      ref={canvasRef}
      width={width}
      height={height}
      className="bg-white border border-gray-200 rounded-lg cursor-default"
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
    />
  )
}

export default WordCloudComponent