// 帖子检测工具库 - 使用结构聚类方法检测网页中的帖子列表

/**
 * DOM元素的结构签名
 */
interface ElementSignature {
  tagName: string
  classNames: string[]
  childrenCount: number
  textLength: number
  linkCount: number
  imageCount: number
  tagSequence: string // 标签名称序列，如 "div>h2>a>span"
}

/**
 * 检测到的帖子元素信息
 */
interface DetectedPost {
  element: Element
  signature: ElementSignature
  title?: string
  link?: string
  preview?: string
}

/**
 * 结构化的帖子信息JSON对象
 */
export interface PostInfo {
  title: string
  xpath: string
  author: string
  time: string
  link: string
}

/**
 * 聚类结果
 */
interface ClusterResult {
  signature: ElementSignature
  elements: Element[]
  confidence: number // 置信度 (0-1)
  posts: DetectedPost[]
}

/**
 * 转义XPath中的特殊字符
 */
function escapeXPathString(str: string): string {
  // 如果字符串中没有单引号，用单引号包围
  if (!str.includes("'")) {
    return `'${str}'`
  }
  // 如果字符串中没有双引号，用双引号包围
  if (!str.includes('"')) {
    return `"${str}"`
  }
  // 如果都有，使用concat函数
  const parts = str.split("'").map(part => `'${part}'`)
  return `concat(${parts.join(", \"'\", ")})`
}

/**
 * 验证XPath是否能正确定位到指定元素
 */
function validateXPath(xpath: string, targetElement: Element): boolean {
  try {
    const result = document.evaluate(
      xpath,
      document,
      null,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null
    )
    return result.singleNodeValue === targetElement
  } catch (error) {
    console.warn(`[XPath验证] XPath "${xpath}" 验证失败:`, error)
    return false
  }
}

/**
 * 生成元素的XPath路径
 */
function getElementXPath(element: Element): string {
  // 优先使用ID属性（如果存在且有效）
  if (element.id && element.id.trim()) {
    const escapedId = escapeXPathString(element.id)
    const idXPath = `//*[@id=${escapedId}]`
    if (validateXPath(idXPath, element)) {
      return idXPath
    }
  }

  // 检查是否有唯一的class属性
  if (element.className && element.className.trim()) {
    const classes = element.className.trim().split(/\s+/).filter(cls => cls.length > 0)
    if (classes.length > 0) {
      // 尝试使用第一个类名生成较短的路径
      const firstClass = classes[0]
      const classXPath = `//*[@class='${firstClass}']`
      if (validateXPath(classXPath, element)) {
        return classXPath
      }
      
      // 尝试使用contains方式匹配class
      const containsClassXPath = `//*[contains(@class, '${firstClass}')]`
      const containsElements = document.evaluate(
        containsClassXPath,
        document,
        null,
        XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
        null
      )
      if (containsElements.snapshotLength === 1 && containsElements.snapshotItem(0) === element) {
        return containsClassXPath
      }
    }
  }

  const parts: string[] = []
  let currentElement = element as Element | null

  while (currentElement && currentElement.nodeType === Node.ELEMENT_NODE) {
    // 停止在body或html标签，避免路径过长
    if (currentElement.tagName.toLowerCase() === 'html') {
      break
    }

    let tagName = currentElement.tagName.toLowerCase()
    
    // 如果有ID，在这里停止并使用ID
    if (currentElement.id && currentElement.id.trim() && currentElement !== element) {
      const escapedId = escapeXPathString(currentElement.id)
      parts.unshift(`//*[@id=${escapedId}]`)
      break
    }

    if (currentElement.parentNode && currentElement.parentNode.nodeType === Node.ELEMENT_NODE) {
      const parent = currentElement.parentNode as Element
      const siblings = Array.from(parent.children)
      const sameTagSiblings = siblings.filter(sibling => 
        sibling.tagName.toLowerCase() === tagName
      )
      
      if (sameTagSiblings.length > 1) {
        // 使用更可靠的索引计算方法
        let index = 1
        for (let i = 0; i < siblings.length; i++) {
          if (siblings[i] === currentElement) {
            break
          }
          if (siblings[i].tagName.toLowerCase() === tagName) {
            index++
          }
        }
        tagName += `[${index}]`
      }
    }
    
    parts.unshift(tagName)
    currentElement = currentElement.parentElement

    // 限制路径深度，避免过长
    if (parts.length > 10) {
      break
    }
  }

  // 如果路径为空，返回通用路径
  if (parts.length === 0) {
    return '//*'
  }

  // 确保路径以/开头
  let xpath = parts[0].startsWith('//*') ? parts.join('/') : '/' + parts.join('/')
  
  // 验证生成的XPath是否正确
  if (validateXPath(xpath, element)) {
    return xpath
  }

  // 如果基本路径验证失败，尝试使用更具体的属性
  console.warn(`[XPath生成] 基本XPath验证失败: ${xpath}`)
  
  // 尝试添加文本内容作为辅助定位
  const textContent = element.textContent?.trim()
  if (textContent && textContent.length > 0 && textContent.length < 50) {
    const shortText = textContent.substring(0, 20).replace(/'/g, '"') // 简单转义
    const textXPath = `//*[contains(text(), '${shortText}')]`
    if (validateXPath(textXPath, element)) {
      return textXPath
    }
  }

  // 尝试使用简单的位置信息
  const tagName = element.tagName.toLowerCase()
  const parent = element.parentElement
  if (parent && parent.tagName.toLowerCase() !== 'html') {
    const siblings = Array.from(parent.children).filter(child => 
      child.tagName.toLowerCase() === tagName
    )
    if (siblings.length > 1) {
      const index = siblings.indexOf(element) + 1
      const parentTag = parent.tagName.toLowerCase()
      // 使用简单的父子关系，避免递归
      const positionXPath = `//${parentTag}/${tagName}[${index}]`
      if (validateXPath(positionXPath, element)) {
        return positionXPath
      }
    }
  }

  // 如果所有方法都失败，返回基本路径并记录警告
  console.warn(`[XPath生成] 无法生成可靠的XPath，返回基本路径: ${xpath}`)
  return xpath
}

/**
 * 提取DOM元素的结构签名
 */
function extractElementSignature(element: Element): ElementSignature {
  // 获取标签名序列（深度限制为3层）
  function getTagSequence(el: Element, depth = 0, maxDepth = 3): string {
    if (depth >= maxDepth) return el.tagName.toLowerCase()
    
    const children = Array.from(el.children)
    if (children.length === 0) return el.tagName.toLowerCase()
    
    // 取前3个子元素的标签序列
    const childSequences = children
      .slice(0, 3)
      .map(child => getTagSequence(child, depth + 1, maxDepth))
      .join(',')
    
    return `${el.tagName.toLowerCase()}>${childSequences}`
  }

  const classNames = Array.from(element.classList).filter(cls => cls.trim() !== '')
  const textLength = element.textContent?.trim().length || 0
  const linkCount = element.querySelectorAll('a').length
  const imageCount = element.querySelectorAll('img').length
  const childrenCount = element.children.length

  return {
    tagName: element.tagName.toLowerCase(),
    classNames,
    childrenCount,
    textLength,
    linkCount,
    imageCount,
    tagSequence: getTagSequence(element)
  }
}

/**
 * 计算两个结构签名的相似度
 */
function calculateSimilarity(sig1: ElementSignature, sig2: ElementSignature): number {
  let score = 0
  let totalWeights = 0

  // 标签名相似度（权重：0.3）
  const tagWeight = 0.3
  if (sig1.tagName === sig2.tagName) {
    score += tagWeight
  }
  totalWeights += tagWeight

  // 标签序列相似度（权重：0.25）
  const sequenceWeight = 0.25
  if (sig1.tagSequence === sig2.tagSequence) {
    score += sequenceWeight
  } else {
    // 计算序列的部分匹配度
    const seq1Parts = sig1.tagSequence.split('>')
    const seq2Parts = sig2.tagSequence.split('>')
    const commonParts = seq1Parts.filter(part => seq2Parts.includes(part))
    const partialScore = commonParts.length / Math.max(seq1Parts.length, seq2Parts.length)
    score += sequenceWeight * partialScore
  }
  totalWeights += sequenceWeight

  // 类名相似度（权重：0.2）
  const classWeight = 0.2
  if (sig1.classNames.length > 0 && sig2.classNames.length > 0) {
    const commonClasses = sig1.classNames.filter(cls => sig2.classNames.includes(cls))
    const classScore = commonClasses.length / Math.max(sig1.classNames.length, sig2.classNames.length)
    score += classWeight * classScore
  }
  totalWeights += classWeight

  // 子元素数量相似度（权重：0.1）
  const childrenWeight = 0.1
  if (sig1.childrenCount > 0 && sig2.childrenCount > 0) {
    const childrenScore = 1 - Math.abs(sig1.childrenCount - sig2.childrenCount) / 
                         Math.max(sig1.childrenCount, sig2.childrenCount)
    score += childrenWeight * childrenScore
  }
  totalWeights += childrenWeight

  // 链接数量相似度（权重：0.1）
  const linkWeight = 0.1
  if (sig1.linkCount === sig2.linkCount) {
    score += linkWeight
  } else if (sig1.linkCount > 0 && sig2.linkCount > 0) {
    const linkScore = 1 - Math.abs(sig1.linkCount - sig2.linkCount) / 
                     Math.max(sig1.linkCount, sig2.linkCount)
    score += linkWeight * linkScore
  }
  totalWeights += linkWeight

  // 图片数量相似度（权重：0.05）
  const imageWeight = 0.05
  if (sig1.imageCount === sig2.imageCount) {
    score += imageWeight
  }
  totalWeights += imageWeight

  return totalWeights > 0 ? score / totalWeights : 0
}

/**
 * 检查链接是否为用户/作者链接
 */
function isUserLink(href: string, linkElement: HTMLAnchorElement): boolean {
  if (!href || !linkElement) return false

  // 1. URL模式检查（扩展现有规则）
  const userUrlPatterns = [
    /\/user\//, /\/users\//, /\/member\//, /\/members\//,
    /\/author\//, /\/authors\//, /\/profile\//, /\/profiles\//,
    /\/people\//, /\/person\//, /\/u\//, /\/@[\w-]+/,
    /\/accounts\//, /\/account\//, /\/用户\//, /\/成员\//,
    /\/作者\//, /\/个人资料\//, /\/档案\//,
    /[?&]user=/, /[?&]profile=/, /[?&]author=/,
    /[?&]uid=/, /[?&]userid=/, /[?&]username=/
  ]

  if (userUrlPatterns.some(pattern => pattern.test(href))) {
    return true
  }

  // 2. CSS类名检查
  const userClassPatterns = [
    'user', 'author', 'profile', 'member', 'username',
    'user-link', 'author-link', 'profile-link', 'member-link',
    'user-name', 'author-name', 'profile-name',
    'avatar-link', 'user-avatar', 'author-avatar'
  ]

  const elementClasses = linkElement.className.toLowerCase()
  if (userClassPatterns.some(pattern => elementClasses.includes(pattern))) {
    return true
  }

  // 3. 父元素上下文检查
  const userContextSelectors = [
    '[class*="user"]', '[class*="author"]', '[class*="profile"]',
    '[class*="member"]', '[class*="avatar"]', '[class*="by-"]',
    '[data-user]', '[data-author]', '[data-profile]'
  ]

  for (const selector of userContextSelectors) {
    if (linkElement.closest(selector)) {
      return true
    }
  }

  // 4. 链接文本特征检查
  const linkText = linkElement.textContent?.trim().toLowerCase() || ''
  const userTextPatterns = [
    /^@\w+/, /^用户:/, /^作者:/, /^by\s+/i,
    /^发布者:/, /^楼主/, /^lz$/
  ]

  if (userTextPatterns.some(pattern => pattern.test(linkText))) {
    return true
  }

  return false
}

/**
 * 提取作者信息
 */
function extractAuthor(element: Element): string {
  const authorSelectors = [
    '[class*="author"]', '[class*="user"]', '[class*="by"]',
    '.author', '.user', '.username', '.by-author',
    '[data-author]', '[data-user]', '[data-username]',
    'a[href*="/user/"]', 'a[href*="/author/"]', 'a[href*="/u/"]',
    '.post-author', '.comment-author', '.article-author'
  ]

  for (const selector of authorSelectors) {
    const authorEl = element.querySelector(selector)
    if (authorEl && authorEl.textContent?.trim()) {
      return authorEl.textContent.trim()
    }
  }

  return ''
}

/**
 * 提取时间信息
 */
function extractTime(element: Element): string {
  const timeSelectors = [
    'time', '[datetime]', '[class*="time"]', '[class*="date"]',
    '.time', '.date', '.timestamp', '.published', '.created',
    '[data-time]', '[data-date]', '[data-timestamp]',
    '.post-time', '.comment-time', '.article-time'
  ]

  for (const selector of timeSelectors) {
    const timeEl = element.querySelector(selector)
    if (timeEl) {
      // 优先使用datetime属性
      const datetime = timeEl.getAttribute('datetime')
      if (datetime) return datetime
      
      // 否则使用文本内容
      const timeText = timeEl.textContent?.trim()
      if (timeText) return timeText
    }
  }

  // 尝试匹配时间格式的文本
  const allText = element.textContent || ''
  const timePatterns = [
    /\d{4}-\d{2}-\d{2}/g,  // 2023-12-01
    /\d{2}\/\d{2}\/\d{4}/g, // 12/01/2023
    /\d+\s*小时前/g,        // 2小时前
    /\d+\s*分钟前/g,        // 30分钟前
    /\d+\s*天前/g,          // 3天前
    /昨天|今天|前天/g        // 相对时间
  ]

  for (const pattern of timePatterns) {
    const matches = allText.match(pattern)
    if (matches && matches.length > 0) {
      return matches[0]
    }
  }

  return ''
}

/**
 * 计算标题匹配分数
 */
function calculateTitleMatchScore(title: string, linkText: string, linkElement: HTMLAnchorElement): number {
  if (!title || !linkText) return 0

  const titleLower = title.toLowerCase().trim()
  const linkTextLower = linkText.toLowerCase().trim()

  // 1. 精确匹配
  if (titleLower === linkTextLower) return 1.0

  // 2. 包含关系（设置最小长度阈值）
  const minMatchLength = Math.min(titleLower.length * 0.6, 15)

  if (titleLower.length >= minMatchLength && linkTextLower.includes(titleLower)) {
    return 0.9
  }

  if (linkTextLower.length >= minMatchLength && titleLower.includes(linkTextLower)) {
    // 检查是否是有意义的包含（避免匹配到单个常见词汇）
    const commonWords = ['的', '和', '与', '或', '在', 'the', 'and', 'or', 'in', 'on', 'at', 'to', 'for']
    if (!commonWords.includes(linkTextLower) && linkTextLower.length >= 3) {
      return 0.8
    }
  }

  // 3. CSS类名加分
  const titleClasses = ['title', 'headline', 'post-title', 'article-title', 'subject', 'topic']
  const hasTitleClass = titleClasses.some(cls =>
    linkElement.className.toLowerCase().includes(cls)
  )
  if (hasTitleClass) return 0.85

  // 4. 标签类型加分
  if (linkElement.closest('h1, h2, h3, h4, h5, h6')) return 0.8

  // 5. 父元素标题相关类名
  const titleParent = linkElement.closest('[class*="title"], [class*="headline"], [class*="subject"]')
  if (titleParent) return 0.75

  return 0
}

/**
 * 查找最佳的标题链接
 */
function findTitleLink(element: Element, title: string): string | null {
  if (!title || title.length < 3) return null

  const titleLinks = element.querySelectorAll('a[href]')
  let bestMatch: { link: string, score: number } | null = null

  console.log(`[链接提取] 开始查找标题链接，标题: "${title}"`)

  for (const linkEl of titleLinks) {
    const linkText = linkEl.textContent?.trim() || ''
    const href = (linkEl as HTMLAnchorElement).href

    // 跳过明确的用户链接
    if (isUserLink(href, linkEl as HTMLAnchorElement)) {
      console.log(`[链接提取] 跳过用户链接: ${href} (文本: "${linkText}")`)
      continue
    }

    // 计算匹配分数
    const score = calculateTitleMatchScore(title, linkText, linkEl as HTMLAnchorElement)
    console.log(`[链接提取] 链接评分: ${score.toFixed(2)} - ${href} (文本: "${linkText}")`)

    if (score > 0.7 && (!bestMatch || score > bestMatch.score)) {
      bestMatch = { link: href, score }
    }
  }

  if (bestMatch) {
    console.log(`[链接提取] 找到最佳标题链接: ${bestMatch.link} (评分: ${bestMatch.score.toFixed(2)})`)
  } else {
    console.log(`[链接提取] 未找到合适的标题链接`)
  }

  return bestMatch?.link || null
}

/**
 * 将检测到的帖子转换为结构化JSON对象
 */
function convertToPostInfo(detectedPost: DetectedPost): PostInfo {
  const element = detectedPost.element

  return {
    title: detectedPost.title || '(无标题)',
    xpath: getElementXPath(element),
    author: extractAuthor(element) || '(无作者)',
    time: extractTime(element) || '(无时间)',
    link: detectedPost.link || '(无链接)'
  }
}

/**
 * 智能选择最佳的帖子链接
 */
function selectBestPostLink(element: Element): string | null {
  const allLinks = Array.from(element.querySelectorAll('a[href]'))

  if (allLinks.length === 0) return null

  console.log(`[智能链接选择] 开始分析 ${allLinks.length} 个链接`)

  // 对所有链接进行评分
  const scoredLinks = allLinks.map((linkEl, index) => {
    const href = (linkEl as HTMLAnchorElement).href
    const text = linkEl.textContent?.trim() || ''

    let score = 0
    const reasons: string[] = []

    // 排除用户链接
    if (isUserLink(href, linkEl as HTMLAnchorElement)) {
      console.log(`[智能链接选择] 链接 ${index + 1}: 用户链接，跳过 - ${href}`)
      return { linkEl, href, score: -1, reasons: ['用户链接'] }
    }

    // 内容相关的CSS类名加分
    const contentClasses = ['post', 'article', 'content', 'title', 'headline', 'subject', 'topic']
    const matchedClasses = contentClasses.filter(cls =>
      linkEl.className.toLowerCase().includes(cls)
    )
    if (matchedClasses.length > 0) {
      score += 10 * matchedClasses.length
      reasons.push(`内容类名: ${matchedClasses.join(', ')}`)
    }

    // 标签类型加分
    if (linkEl.closest('h1, h2, h3, h4, h5, h6')) {
      score += 8
      reasons.push('标题标签内')
    }
    if (linkEl.closest('[class*="title"], [class*="headline"], [class*="subject"]')) {
      score += 6
      reasons.push('标题容器内')
    }

    // 链接文本长度合理性
    if (text.length > 10 && text.length < 200) {
      score += 5
      reasons.push('文本长度合理')
    } else if (text.length >= 200) {
      score -= 2
      reasons.push('文本过长')
    } else if (text.length <= 3) {
      score -= 3
      reasons.push('文本过短')
    }

    // URL特征分析
    const postUrlPatterns = [
      /\/post\//, /\/posts\//, /\/article\//, /\/articles\//,
      /\/topic\//, /\/topics\//, /\/thread\//, /\/threads\//,
      /\/story\//, /\/stories\//, /\/item\//, /\/items\//,
      /\/discussion\//, /\/discussions\//, /\/question\//, /\/questions\//
    ]

    const matchedPatterns = postUrlPatterns.filter(pattern => pattern.test(href))
    if (matchedPatterns.length > 0) {
      score += 8
      reasons.push('帖子URL模式')
    }

    // 避免常见的非内容链接
    const avoidPatterns = [
      /\/edit/, /\/delete/, /\/reply/, /\/comment/, /\/share/,
      /\/like/, /\/vote/, /\/bookmark/, /\/report/, /\/flag/
    ]

    if (avoidPatterns.some(pattern => pattern.test(href))) {
      score -= 5
      reasons.push('操作链接')
    }

    // 相对位置加分（假设重要链接通常在前面）
    if (index < 3) {
      score += 2
      reasons.push('位置靠前')
    }

    console.log(`[智能链接选择] 链接 ${index + 1}: 评分 ${score} - ${href} (${reasons.join(', ')})`)

    return { linkEl, href, score, reasons }
  })

  // 选择得分最高的链接
  const validLinks = scoredLinks.filter(item => item.score > 0)

  if (validLinks.length === 0) {
    console.log(`[智能链接选择] 没有找到有效的内容链接`)
    return null
  }

  const bestLink = validLinks.sort((a, b) => b.score - a.score)[0]
  console.log(`[智能链接选择] 选择最佳链接: ${bestLink.href} (评分: ${bestLink.score})`)

  return bestLink.href
}

/**
 * 提取帖子信息
 */
function extractPostInfo(element: Element): DetectedPost {
  const signature = extractElementSignature(element)
  const className = element.className || ''
  const isTargetPost = className.includes('from_343179') || className.includes('from_303973')

  if (isTargetPost) {
    console.debug(`[调试-特定帖子] 开始提取帖子信息:`, {
      className: className,
      element: element
    })
  }

  // 尝试提取标题
  let title = ''
  const titleSelectors = [
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    '[class*="title"]', '[class*="heading"]',
    'a[href]', '.title', '.headline', '.post-title'
  ]

  for (const selector of titleSelectors) {
    const titleEl = element.querySelector(selector)
    if (titleEl && titleEl.textContent?.trim()) {
      title = titleEl.textContent.trim()
      if (isTargetPost) {
        console.debug(`[调试-特定帖子] 找到标题:`, {
          selector: selector,
          title: title,
          titleElement: titleEl
        })
      }
      break
    }
  }

  if (isTargetPost) {
    console.debug(`[调试-特定帖子] 标题提取结果:`, { title: title })
  }

  console.log(`[帖子信息提取] 开始提取链接，标题: "${title}"`)

  // 改进的链接提取逻辑
  let link = ''

  // 1. 优先查找高质量的标题链接
  if (title) {
    link = findTitleLink(element, title)
    if (isTargetPost && link) {
      console.debug(`[调试-特定帖子] 找到标题链接:`, { link: link })
    }
  }

  // 2. 如果没找到标题链接，使用智能选择
  if (!link) {
    console.log(`[帖子信息提取] 标题链接未找到，使用智能选择`)
    link = selectBestPostLink(element)
    if (isTargetPost && link) {
      console.debug(`[调试-特定帖子] 智能选择链接:`, { link: link })
    }
  }

  // 3. 最后的备选方案
  if (!link) {
    console.log(`[帖子信息提取] 智能选择失败，使用备选方案`)
    const firstLink = element.querySelector('a[href]')
    if (firstLink && !isUserLink((firstLink as HTMLAnchorElement).href, firstLink as HTMLAnchorElement)) {
      link = (firstLink as HTMLAnchorElement).href
      console.log(`[帖子信息提取] 使用第一个非用户链接: ${link}`)
      if (isTargetPost) {
        console.debug(`[调试-特定帖子] 备选方案链接:`, { link: link })
      }
    }
  }

  // 尝试提取预览文本
  let preview = ''
  const textContent = element.textContent?.trim() || ''
  if (textContent.length > title.length) {
    preview = textContent.substring(0, 200).replace(title, '').trim()
  }

  if (isTargetPost) {
    console.debug(`[调试-特定帖子] 最终提取结果:`, {
      className: className,
      title: title,
      link: link,
      preview: preview.substring(0, 50) + '...'
    })
  }

  console.log(`[帖子信息提取] 最终结果 - 标题: "${title}", 链接: "${link}"`)

  return {
    element,
    signature,
    title,
    link,
    preview
  }
}

/**
 * 对DOM元素进行结构聚类
 */
function clusterElements(elements: Element[], minClusterSize = 3, similarityThreshold = 0.7): ClusterResult[] {
  console.log(`[帖子检测] 开始聚类分析，候选元素数量: ${elements.length}`)

  // 检查特定帖子是否在聚类输入中
  const targetPost1Index = elements.findIndex(el => el.className?.includes('from_343179'))
  const targetPost2Index = elements.findIndex(el => el.className?.includes('from_303973'))
  console.debug(`[调试-特定帖子] 聚类输入检查:`, {
    post1Index: targetPost1Index,
    post2Index: targetPost2Index,
    post1Found: targetPost1Index >= 0,
    post2Found: targetPost2Index >= 0
  })

  const signatures = elements.map(extractElementSignature)
  const clusters: ClusterResult[] = []
  const used = new Set<number>()

  for (let i = 0; i < elements.length; i++) {
    if (used.has(i)) continue

    const currentElement = elements[i]
    const currentClassName = currentElement.className || ''
    const isTargetPost = currentClassName.includes('from_343179') || currentClassName.includes('from_303973')

    if (isTargetPost) {
      console.debug(`[调试-特定帖子] 开始处理目标帖子 ${i}:`, {
        className: currentClassName,
        signature: signatures[i]
      })
    }

    const cluster = {
      signature: signatures[i],
      elements: [elements[i]],
      confidence: 0,
      posts: []
    }

    // 查找相似的元素
    for (let j = i + 1; j < elements.length; j++) {
      if (used.has(j)) continue

      const similarity = calculateSimilarity(signatures[i], signatures[j])
      const otherElement = elements[j]
      const otherClassName = otherElement.className || ''
      const isOtherTargetPost = otherClassName.includes('from_343179') || otherClassName.includes('from_303973')

      if (isTargetPost || isOtherTargetPost) {
        console.debug(`[调试-特定帖子] 相似度计算:`, {
          element1: i,
          element2: j,
          class1: currentClassName,
          class2: otherClassName,
          similarity: similarity.toFixed(3),
          threshold: similarityThreshold,
          willCluster: similarity >= similarityThreshold,
          sig1: signatures[i],
          sig2: signatures[j]
        })
      }

      // 特别检查两个目标帖子之间的相似度
      if ((currentClassName.includes('from_343179') && otherClassName.includes('from_303973')) ||
          (currentClassName.includes('from_303973') && otherClassName.includes('from_343179'))) {
        console.debug(`[调试-特定帖子] 两个目标帖子之间的相似度:`, {
          similarity: similarity.toFixed(3),
          threshold: similarityThreshold,
          willClusterTogether: similarity >= similarityThreshold,
          sig1: signatures[i],
          sig2: signatures[j],
          详细分析: '这两个帖子应该聚类在一起'
        })
      }

      console.log(`[帖子检测] 元素 ${i} 与元素 ${j} 相似度: ${similarity.toFixed(3)}`)

      if (similarity >= similarityThreshold) {
        cluster.elements.push(elements[j])
        used.add(j)

        if (isTargetPost || isOtherTargetPost) {
          console.debug(`[调试-特定帖子] 成功聚类:`, {
            mainElement: i,
            addedElement: j,
            clusterSize: cluster.elements.length
          })
        }
      }
    }

    if (isTargetPost) {
      console.debug(`[调试-特定帖子] 聚类结果:`, {
        elementIndex: i,
        className: currentClassName,
        clusterSize: cluster.elements.length,
        minRequired: minClusterSize,
        willBeKept: cluster.elements.length >= minClusterSize
      })
    }

    // 只保留足够大的聚类
    if (cluster.elements.length >= minClusterSize) {
      used.add(i)

      // 计算置信度
      cluster.confidence = Math.min(cluster.elements.length / 10, 1)

      // 提取帖子信息
      cluster.posts = cluster.elements.map(extractPostInfo)

      clusters.push(cluster)
      console.log(`[帖子检测] 发现聚类: ${cluster.elements.length} 个元素，置信度: ${cluster.confidence.toFixed(3)}`)

      if (isTargetPost) {
        console.debug(`[调试-特定帖子] 目标帖子成功加入聚类:`, {
          clusterIndex: clusters.length - 1,
          clusterSize: cluster.elements.length,
          confidence: cluster.confidence
        })
      }
    } else if (isTargetPost) {
      console.debug(`[调试-特定帖子] 目标帖子聚类太小被丢弃:`, {
        elementIndex: i,
        clusterSize: cluster.elements.length,
        minRequired: minClusterSize
      })
    }
  }

  // 分析聚类结果中的目标帖子分布
  console.debug(`[调试-特定帖子] 聚类结果分析:`)
  clusters.forEach((cluster, clusterIndex) => {
    const hasPost1 = cluster.elements.some(el => el.className?.includes('from_343179'))
    const hasPost2 = cluster.elements.some(el => el.className?.includes('from_303973'))

    if (hasPost1 || hasPost2) {
      console.debug(`[调试-特定帖子] 聚类 ${clusterIndex}:`, {
        size: cluster.elements.length,
        confidence: cluster.confidence,
        hasPost1: hasPost1,
        hasPost2: hasPost2,
        signature: cluster.signature,
        elementClasses: cluster.elements.map(el => el.className)
      })
    }
  })

  return clusters.sort((a, b) => b.confidence - a.confidence)
}

/**
 * 调试模式控制
 */
let debugMode = false

/**
 * 设置调试模式
 */
export function setDebugMode(enabled: boolean) {
  debugMode = enabled
  console.log(`[帖子检测] 调试模式 ${enabled ? '开启' : '关闭'}`)
}

/**
 * 调试日志函数
 */
function debugLog(message: string, ...args: any[]) {
  if (debugMode) {
    console.log(`[DEBUG] ${message}`, ...args)
  }
}

/**
 * 主列表评分标准
 */
interface MainListScore {
  cluster: ClusterResult
  score: number
  reasons: string[]
  isMainList: boolean
}

/**
 * 识别页面中的主要帖子列表
 * 基于多个因素评分，选择最可能是主列表的聚类
 */
function identifyMainPostList(clusters: ClusterResult[]): ClusterResult | null {
  if (clusters.length === 0) {
    console.log('[主列表识别] 没有找到任何聚类')
    return null
  }

  if (clusters.length === 1) {
    console.log('[主列表识别] 只有一个聚类，直接选择为主列表')
    return clusters[0]
  }

  console.log(`[主列表识别] 开始分析 ${clusters.length} 个聚类，识别主要帖子列表`)

  const scores: MainListScore[] = clusters.map((cluster, index) => {
    let score = 0
    const reasons: string[] = []

    // 检查这个聚类是否包含目标帖子
    const hasPost1 = cluster.elements.some(el => el.className?.includes('from_343179'))
    const hasPost2 = cluster.elements.some(el => el.className?.includes('from_303973'))

    // 1. 聚类大小评分（权重：30%）
    const sizeScore = Math.min(cluster.elements.length / 10, 1) * 30
    score += sizeScore
    reasons.push(`聚类大小: ${cluster.elements.length}个元素 (+${sizeScore.toFixed(1)})`)

    // 2. 置信度评分（权重：20%）
    const confidenceScore = cluster.confidence * 20
    score += confidenceScore
    reasons.push(`置信度: ${cluster.confidence.toFixed(3)} (+${confidenceScore.toFixed(1)})`)

    // 3. 位置评分（权重：25%）
    const positionScore = calculatePositionScore(cluster.elements)
    score += positionScore
    reasons.push(`位置评分: +${positionScore.toFixed(1)}`)

    // 4. 内容质量评分（权重：15%）
    const qualityScore = calculateContentQualityScore(cluster.posts)
    score += qualityScore
    reasons.push(`内容质量: +${qualityScore.toFixed(1)}`)

    // 5. 区域类型评分（权重：10%）
    const areaScore = calculateAreaTypeScore(cluster.elements)
    score += areaScore
    reasons.push(`区域类型: +${areaScore.toFixed(1)}`)

    const logMessage = `[主列表识别] 聚类 ${index + 1} 评分: ${score.toFixed(1)} (${reasons.join(', ')})`

    if (hasPost1 || hasPost2) {
      console.debug(`[调试-特定帖子] ${logMessage}`, {
        hasPost1: hasPost1,
        hasPost2: hasPost2,
        clusterSize: cluster.elements.length,
        confidence: cluster.confidence
      })
    }

    console.log(logMessage)

    return {
      cluster,
      score,
      reasons,
      isMainList: false
    }
  })

  // 按评分排序
  scores.sort((a, b) => b.score - a.score)

  // 选择得分最高的作为主列表
  const mainList = scores[0]
  mainList.isMainList = true

  // 检查是否有明显的主列表（得分差距足够大）
  if (scores.length > 1) {
    const scoreDiff = mainList.score - scores[1].score
    if (scoreDiff < 10) {
      console.warn(`[主列表识别] 警告: 主列表得分差距较小 (${scoreDiff.toFixed(1)})，可能存在识别不准确的情况`)
    }
  }

  console.log(`[主列表识别] 选择聚类作为主列表，得分: ${mainList.score.toFixed(1)}`)
  console.log(`[主列表识别] 主列表包含 ${mainList.cluster.elements.length} 个帖子元素`)

  return mainList.cluster
}

/**
 * 计算聚类元素的位置评分
 * 主列表通常位于页面中心区域，而不是侧边栏
 */
function calculatePositionScore(elements: Element[]): number {
  if (elements.length === 0) return 0

  let totalScore = 0
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  elements.forEach(element => {
    const rect = element.getBoundingClientRect()
    let elementScore = 0

    // 水平位置评分（中心区域得分更高）
    const centerX = rect.left + rect.width / 2
    const horizontalRatio = centerX / viewportWidth

    if (horizontalRatio >= 0.2 && horizontalRatio <= 0.8) {
      // 在中心区域（20%-80%）
      elementScore += 15
    } else if (horizontalRatio >= 0.1 && horizontalRatio <= 0.9) {
      // 在较宽的中心区域（10%-90%）
      elementScore += 10
    } else {
      // 在边缘区域，可能是侧边栏
      elementScore += 2
    }

    // 垂直位置评分（页面上半部分得分更高）
    const centerY = rect.top + rect.height / 2
    const verticalRatio = centerY / viewportHeight

    if (verticalRatio <= 0.7) {
      // 在页面上部70%区域
      elementScore += 8
    } else {
      // 在页面底部
      elementScore += 3
    }

    // 元素大小评分（合理大小的元素得分更高）
    if (rect.width > 300 && rect.width < viewportWidth * 0.8) {
      elementScore += 5
    }

    totalScore += elementScore
  })

  return Math.min(totalScore / elements.length, 25) // 最大25分
}

/**
 * 计算内容质量评分
 * 基于帖子信息的完整性和质量
 */
function calculateContentQualityScore(posts: DetectedPost[]): number {
  if (posts.length === 0) return 0

  let totalScore = 0

  posts.forEach(post => {
    let postScore = 0

    // 标题质量
    if (post.title && post.title.length > 5) {
      postScore += 5
      if (post.title.length > 10 && post.title.length < 200) {
        postScore += 3
      }
    }

    // 链接质量
    if (post.link && post.link !== '(无链接)') {
      postScore += 4
      // 检查是否是内容链接而非用户链接
      if (!isUserLink(post.link, null)) {
        postScore += 3
      }
    }

    // 预览文本
    if (post.preview && post.preview.length > 10) {
      postScore += 2
    }

    totalScore += postScore
  })

  return Math.min(totalScore / posts.length, 15) // 最大15分
}

/**
 * 计算区域类型评分
 * 基于元素的CSS类名和HTML结构判断是否为主内容区域
 */
function calculateAreaTypeScore(elements: Element[]): number {
  if (elements.length === 0) return 0

  let totalScore = 0

  // 主内容区域的常见类名和标识
  const mainContentIndicators = [
    'main', 'content', 'posts', 'articles', 'feed', 'list',
    'primary', 'central', 'body', 'container'
  ]

  // 侧边栏和次要区域的标识
  const sidebarIndicators = [
    'sidebar', 'aside', 'secondary', 'widget', 'recommended',
    'related', 'popular', 'trending', 'hot', 'recent'
  ]

  elements.forEach(element => {
    let elementScore = 5 // 基础分数

    // 检查元素及其父容器的类名
    let currentElement: Element | null = element
    let depth = 0

    while (currentElement && depth < 5) {
      const className = currentElement.className.toLowerCase()

      // 主内容区域加分
      mainContentIndicators.forEach(indicator => {
        if (className.includes(indicator)) {
          elementScore += 2
        }
      })

      // 侧边栏区域减分
      sidebarIndicators.forEach(indicator => {
        if (className.includes(indicator)) {
          elementScore -= 3
        }
      })

      currentElement = currentElement.parentElement
      depth++
    }

    // 检查是否在明确的主内容标签中
    if (element.closest('main, [role="main"], .main-content, #main-content')) {
      elementScore += 5
    }

    // 检查是否在侧边栏中
    if (element.closest('aside, .sidebar, .side-bar, [role="complementary"]')) {
      elementScore -= 5
    }

    totalScore += Math.max(elementScore, 0)
  })

  return Math.min(totalScore / elements.length, 10) // 最大10分
}

/**
 * 分析链接提取结果
 */
function analyzeLinkExtractionResults(clusters: ClusterResult[]) {
  const stats = {
    totalPosts: 0,
    postsWithLinks: 0,
    postsWithUserLinks: 0,
    postsWithContentLinks: 0,
    linkTypes: new Map<string, number>()
  }

  clusters.forEach(cluster => {
    cluster.posts.forEach(post => {
      stats.totalPosts++

      if (post.link) {
        stats.postsWithLinks++

        // 创建临时链接元素用于检测
        const tempLink = document.createElement('a')
        tempLink.href = post.link

        if (isUserLink(post.link, tempLink)) {
          stats.postsWithUserLinks++
        } else {
          stats.postsWithContentLinks++
        }

        // 分析链接类型
        const url = new URL(post.link)
        const pathSegments = url.pathname.split('/').filter(seg => seg.length > 0)
        const linkType = pathSegments.length > 0 ? pathSegments[0] : 'root'
        stats.linkTypes.set(linkType, (stats.linkTypes.get(linkType) || 0) + 1)
      }
    })
  })

  console.group('[链接提取分析]')
  console.log(`总帖子数: ${stats.totalPosts}`)
  console.log(`有链接的帖子: ${stats.postsWithLinks} (${(stats.postsWithLinks / stats.totalPosts * 100).toFixed(1)}%)`)
  console.log(`用户链接: ${stats.postsWithUserLinks} (${(stats.postsWithUserLinks / stats.postsWithLinks * 100).toFixed(1)}%)`)
  console.log(`内容链接: ${stats.postsWithContentLinks} (${(stats.postsWithContentLinks / stats.postsWithLinks * 100).toFixed(1)}%)`)
  console.log('链接类型分布:', Object.fromEntries(stats.linkTypes))
  console.groupEnd()

  return stats
}

/**
 * 检测页面中的帖子列表
 */
export function detectPostLists(): ClusterResult[] {
  console.log('[帖子检测] 开始检测页面中的帖子列表...')

  // 排除的容器选择器
  const excludeSelectors = [
    'script', 'style', 'nav', 'footer', 'header',
    '.navigation', '.sidebar', '.menu', '.ads', '.advertisement'
  ]

  // 候选容器选择器（可能包含帖子列表的容器）
  const candidateSelectors = [
    'article', '.post', '.item', '.entry', '.card',
    '[class*="post"]', '[class*="item"]', '[class*="entry"]',
    '[class*="article"]', '[class*="card"]', '[class*="list"]',
    'li', '.row', '[class*="row"]', '.content', '[class*="content"]',
    // 添加更通用的选择器来捕获各种论坛结构
    '.cell', '.topic', '.thread', '.message',
    '[class*="cell"]', '[class*="topic"]', '[class*="thread"]'
  ]

  const allCandidates: Element[] = []

  // 首先检查目标帖子是否存在于页面中
  const targetPosts = document.querySelectorAll('.from_343179, .from_303973')
  console.debug(`[调试-特定帖子] 页面中的目标帖子:`, {
    found: targetPosts.length,
    elements: Array.from(targetPosts).map(el => ({
      className: el.className,
      tagName: el.tagName,
      textLength: el.textContent?.trim().length || 0
    }))
  })

  // 收集候选元素
  for (const selector of candidateSelectors) {
    try {
      const elements = document.querySelectorAll(selector)
      debugLog(`选择器 "${selector}" 找到 ${elements.length} 个元素`)

      // 检查这个选择器是否能匹配到目标帖子
      const targetMatches = Array.from(elements).filter(el => {
        const className = el.className || ''
        return className.includes('from_343179') || className.includes('from_303973')
      })

      if (targetMatches.length > 0) {
        console.debug(`[调试-特定帖子] 选择器 "${selector}" 匹配到目标帖子:`, {
          matchCount: targetMatches.length,
          matches: targetMatches.map(el => el.className)
        })
      }

      elements.forEach((el, elIndex) => {
        // 针对特定帖子的调试日志
        const className = el.className || ''
        const isTargetPost = className.includes('from_343179') || className.includes('from_303973')

        if (isTargetPost) {
          console.debug(`[调试-特定帖子] 发现目标帖子元素:`, {
            selector: selector,
            elementIndex: elIndex,
            className: className,
            textContent: el.textContent?.substring(0, 100) + '...',
            textLength: el.textContent?.trim().length || 0,
            element: el
          })
        }

        // 检查是否在排除列表中
        const shouldExclude = excludeSelectors.some(excludeSelector => {
          const matches = el.matches(excludeSelector)
          const closest = el.closest(excludeSelector)

          if (isTargetPost && (matches || closest)) {
            console.debug(`[调试-特定帖子] 被排除选择器匹配:`, {
              className: className,
              excludeSelector: excludeSelector,
              matches: matches,
              closest: !!closest
            })
          }

          return matches || closest
        })

        // 检查文本长度条件
        const hasText = el.textContent && el.textContent.trim().length > 20

        if (isTargetPost) {
          console.debug(`[调试-特定帖子] 添加条件检查:`, {
            className: className,
            shouldExclude: shouldExclude,
            hasText: hasText,
            textLength: el.textContent?.trim().length || 0,
            willBeAdded: !shouldExclude && hasText
          })
        }

        if (!shouldExclude && hasText) {
          allCandidates.push(el)

          if (isTargetPost) {
            console.debug(`[调试-特定帖子] 成功添加到候选列表:`, {
              className: className,
              candidateIndex: allCandidates.length - 1
            })
          }
        } else if (isTargetPost) {
          console.debug(`[调试-特定帖子] 未添加到候选列表:`, {
            className: className,
            reason: shouldExclude ? '被排除' : '文本长度不足'
          })
        }
      })
    } catch (error) {
      console.warn(`[帖子检测] 选择器 "${selector}" 执行失败:`, error)
    }
  }

  // 分析为什么目标帖子没有被选择器匹配
  if (targetPosts.length > 0) {
    console.debug(`[调试-特定帖子] 分析目标帖子为什么没有被匹配:`)
    targetPosts.forEach((post, index) => {
      const matchedSelectors: string[] = []
      candidateSelectors.forEach(selector => {
        try {
          if (post.matches(selector)) {
            matchedSelectors.push(selector)
          }
        } catch (e) {
          // 忽略无效选择器
        }
      })

      console.debug(`[调试-特定帖子] 目标帖子 ${index + 1} 匹配的选择器:`, {
        className: post.className,
        matchedSelectors: matchedSelectors,
        totalSelectors: candidateSelectors.length
      })
    })
  }

  console.log(`[帖子检测] 收集到的候选元素总数: ${allCandidates.length}`)

  // 检查收集阶段的目标帖子
  const collectedPost1 = allCandidates.find(el => el.className?.includes('from_343179'))
  const collectedPost2 = allCandidates.find(el => el.className?.includes('from_303973'))
  console.debug(`[调试-特定帖子] 收集阶段状态:`, {
    totalCandidates: allCandidates.length,
    post1Collected: !!collectedPost1,
    post2Collected: !!collectedPost2,
    post1Class: collectedPost1?.className,
    post2Class: collectedPost2?.className
  })

  // 去重（移除重复的元素）
  const uniqueCandidates = Array.from(new Set(allCandidates))
  console.log(`[帖子检测] 去重后的候选元素数量: ${uniqueCandidates.length}`)

  // 检查特定帖子是否在去重后的候选列表中
  const targetPost1 = uniqueCandidates.find(el => el.className?.includes('from_343179'))
  const targetPost2 = uniqueCandidates.find(el => el.className?.includes('from_303973'))
  console.debug(`[调试-特定帖子] 去重后状态:`, {
    post1Found: !!targetPost1,
    post2Found: !!targetPost2,
    post1Class: targetPost1?.className,
    post2Class: targetPost2?.className,
    removedCount: allCandidates.length - uniqueCandidates.length
  })

  // 按元素大小过滤（太小的元素可能不是帖子）
  const filteredCandidates = uniqueCandidates.filter(el => {
    const rect = el.getBoundingClientRect()
    const hasContent = el.textContent && el.textContent.trim().length > 20
    const hasReasonableSize = rect.width > 100 && rect.height > 30

    const className = el.className || ''
    if (className.includes('from_343179') || className.includes('from_303973')) {
      console.debug(`[调试-特定帖子] 尺寸过滤检查:`, {
        className: className,
        rect: { width: rect.width, height: rect.height },
        hasContent: hasContent,
        hasReasonableSize: hasReasonableSize,
        willPass: hasContent && hasReasonableSize
      })
    }

    return hasContent && hasReasonableSize
  })

  console.log(`[帖子检测] 尺寸过滤后的候选元素数量: ${filteredCandidates.length}`)

  // 检查特定帖子是否通过了尺寸过滤
  const filteredPost1 = filteredCandidates.find(el => el.className?.includes('from_343179'))
  const filteredPost2 = filteredCandidates.find(el => el.className?.includes('from_303973'))
  console.debug(`[调试-特定帖子] 尺寸过滤后状态:`, {
    post1Passed: !!filteredPost1,
    post2Passed: !!filteredPost2
  })

  // 进行聚类分析
  const clusters = clusterElements(filteredCandidates, 3, 0.6)

  console.log(`[帖子检测] 检测完成，发现 ${clusters.length} 个可能的帖子列表`)

  // 分析链接提取结果
  if (clusters.length > 0) {
    analyzeLinkExtractionResults(clusters)
  }

  // 识别主要帖子列表
  const mainList = identifyMainPostList(clusters)

  if (mainList) {
    // 检查主列表中是否包含目标帖子
    const hasTargetPost1 = mainList.elements.some(el => el.className?.includes('from_343179'))
    const hasTargetPost2 = mainList.elements.some(el => el.className?.includes('from_303973'))

    console.debug(`[调试-特定帖子] 主列表选择结果:`, {
      mainListFound: true,
      hasTargetPost1: hasTargetPost1,
      hasTargetPost2: hasTargetPost2,
      mainListSize: mainList.elements.length,
      mainListConfidence: mainList.confidence
    })

    console.group('[帖子检测] 主列表分析结果')
    console.log('主列表元素数量:', mainList.elements.length)
    console.log('主列表置信度:', mainList.confidence.toFixed(3))
    console.log('主列表结构签名:', mainList.signature)

    // 转换主列表的帖子为JSON对象
    const mainListPostsJson = mainList.posts.map(convertToPostInfo)

    console.log('主列表帖子JSON对象:')
    mainListPostsJson.forEach((postJson, postIndex) => {
      const isTargetPost = postJson.xpath.includes('from_343179') || postJson.xpath.includes('from_303973')
      console.log(`  帖子 ${postIndex + 1}:`, postJson)

      if (isTargetPost) {
        console.debug(`[调试-特定帖子] 在主列表中找到目标帖子:`, {
          index: postIndex,
          title: postJson.title,
          xpath: postJson.xpath,
          link: postJson.link
        })
      }
    })

    // 检查目标帖子是否在最终结果中
    const finalTargetPost1 = mainListPostsJson.find(post => post.xpath.includes('from_343179'))
    const finalTargetPost2 = mainListPostsJson.find(post => post.xpath.includes('from_303973'))
    console.debug(`[调试-特定帖子] 最终结果检查:`, {
      post1InFinal: !!finalTargetPost1,
      post2InFinal: !!finalTargetPost2,
      post1Title: finalTargetPost1?.title,
      post2Title: finalTargetPost2?.title
    })

    // 输出主列表的完整JSON数组
    console.log('主列表完整JSON数据:')
    console.log(JSON.stringify(mainListPostsJson, null, 2))

    // 输出统计信息
    console.log(`[帖子检测] 主列表统计: 共 ${mainListPostsJson.length} 个帖子`)
    console.log(`[帖子检测] 有效标题: ${mainListPostsJson.filter(p => p.title !== '(无标题)').length} 个`)
    console.log(`[帖子检测] 有效作者: ${mainListPostsJson.filter(p => p.author !== '(无作者)').length} 个`)
    console.log(`[帖子检测] 有效时间: ${mainListPostsJson.filter(p => p.time !== '(无时间)').length} 个`)
    console.log(`[帖子检测] 有效链接: ${mainListPostsJson.filter(p => p.link !== '(无链接)').length} 个`)
    console.groupEnd()
  } else {
    console.warn('[帖子检测] 警告: 未能识别出主要帖子列表')
  }

  // 输出所有聚类的详细信息（用于调试）
  if (debugMode && clusters.length > 0) {
    console.group('[帖子检测] 所有聚类详细信息（调试模式）')
    clusters.forEach((cluster, index) => {
      console.group(`聚类 ${index + 1}`)
      console.log('元素数量:', cluster.elements.length)
      console.log('置信度:', cluster.confidence.toFixed(3))
      console.log('结构签名:', cluster.signature)
      console.log('是否为主列表:', cluster === mainList ? '是' : '否')
      console.groupEnd()
    })
    console.groupEnd()
  }

  return clusters
}

/**
 * 高亮显示检测到的帖子列表（用于调试）
 */
export function highlightDetectedPosts(clusters: ClusterResult[]) {
  // 移除之前的高亮
  document.querySelectorAll('[data-post-highlight]').forEach(el => {
    (el as HTMLElement).style.outline = ''
    el.removeAttribute('data-post-highlight')
  })

  // 添加新的高亮
  clusters.forEach((cluster, clusterIndex) => {
    const colors = ['red', 'blue', 'green', 'orange', 'purple']
    const color = colors[clusterIndex % colors.length]
    
    cluster.elements.forEach(el => {
      (el as HTMLElement).style.outline = `2px solid ${color}`
      el.setAttribute('data-post-highlight', `cluster-${clusterIndex}`)
    })
  })

  console.log(`[帖子检测] 已高亮显示 ${clusters.length} 个聚类的帖子`)
}

/**
 * 获取检测到的帖子JSON数据（仅从主列表）
 * 实现单一列表原则：只从识别出的主要帖子列表中提取数据
 */
export function getPostsAsJson(): PostInfo[] {
  console.log('[帖子提取] 开始提取主列表帖子数据...')

  const clusters = detectPostLists()

  // 识别主要帖子列表
  const mainList = identifyMainPostList(clusters)

  if (!mainList) {
    console.warn('[帖子提取] 警告: 未能识别出主要帖子列表，返回空数组')
    return []
  }

  console.log(`[帖子提取] 从主列表提取帖子数据，包含 ${mainList.elements.length} 个元素`)

  // 只从主列表中提取帖子数据
  const mainListPostsJson = mainList.posts.map(convertToPostInfo)

  // 去重逻辑：基于标题去重，保留更完整的信息
  const uniquePosts = new Map<string, PostInfo>()

  mainListPostsJson.forEach(post => {
    const title = post.title.trim()

    // 跳过无效标题
    if (!title || title === '(无标题)' || title.length < 5) {
      console.log(`[帖子提取] 跳过无效标题: "${title}"`)
      return
    }

    const existing = uniquePosts.get(title)

    if (!existing) {
      // 如果是新标题，直接添加
      uniquePosts.set(title, post)
      console.log(`[帖子提取] 添加新帖子: "${title}"`)
    } else {
      // 如果标题已存在，选择更好的版本
      const currentScore = calculatePostQuality(post)
      const existingScore = calculatePostQuality(existing)

      if (currentScore > existingScore) {
        uniquePosts.set(title, post)
        console.log(`[帖子提取] 替换重复帖子: "${title}" (新评分: ${currentScore}, 旧评分: ${existingScore})`)
      } else {
        console.log(`[帖子提取] 跳过重复帖子: "${title}" (当前评分: ${currentScore}, 已有评分: ${existingScore})`)
      }
    }
  })

  const result = Array.from(uniquePosts.values())

  console.log(`[帖子提取] 提取完成`)
  console.log(`[帖子提取] 主列表原始帖子数量: ${mainListPostsJson.length}`)
  console.log(`[帖子提取] 去重后帖子数量: ${result.length}`)
  console.log(`[帖子提取] 数据来源: 单一主列表 (避免了侧边栏、推荐区域等次要列表的干扰)`)

  // 输出最终结果的统计信息
  if (result.length > 0) {
    console.group('[帖子提取] 最终结果统计')
    console.log(`总帖子数: ${result.length}`)
    console.log(`有效标题: ${result.filter(p => p.title !== '(无标题)').length} 个`)
    console.log(`有效作者: ${result.filter(p => p.author !== '(无作者)').length} 个`)
    console.log(`有效时间: ${result.filter(p => p.time !== '(无时间)').length} 个`)
    console.log(`有效链接: ${result.filter(p => p.link !== '(无链接)').length} 个`)
    console.groupEnd()
  }

  return result
}

/**
 * 获取所有检测到的聚类信息（用于调试和分析）
 */
export function getAllClustersInfo(): {
  clusters: ClusterResult[]
  mainList: ClusterResult | null
  analysis: {
    totalClusters: number
    mainListIndex: number
    clusterSizes: number[]
    confidenceScores: number[]
  }
} {
  console.log('[聚类分析] 获取所有聚类的详细信息...')

  const clusters = detectPostLists()
  const mainList = identifyMainPostList(clusters)

  const analysis = {
    totalClusters: clusters.length,
    mainListIndex: mainList ? clusters.indexOf(mainList) : -1,
    clusterSizes: clusters.map(c => c.elements.length),
    confidenceScores: clusters.map(c => c.confidence)
  }

  console.log('[聚类分析] 分析结果:', analysis)

  return {
    clusters,
    mainList,
    analysis
  }
}

/**
 * 高亮显示主列表（调试功能）
 */
export function highlightMainList(): void {
  console.log('[调试高亮] 开始高亮显示主列表...')

  // 清除之前的高亮
  document.querySelectorAll('[data-post-highlight]').forEach(el => {
    el.removeAttribute('data-post-highlight')
    ;(el as HTMLElement).style.border = ''
    ;(el as HTMLElement).style.boxShadow = ''
  })

  const { mainList, clusters } = getAllClustersInfo()

  if (!mainList) {
    console.warn('[调试高亮] 未找到主列表，无法高亮')
    return
  }

  // 高亮主列表元素（绿色边框）
  mainList.elements.forEach((element, index) => {
    const htmlElement = element as HTMLElement
    htmlElement.setAttribute('data-post-highlight', 'main')
    htmlElement.style.border = '3px solid #22c55e'
    htmlElement.style.boxShadow = '0 0 10px rgba(34, 197, 94, 0.3)'

    console.log(`[调试高亮] 主列表元素 ${index + 1} 已高亮`)
  })

  // 高亮其他聚类元素（红色边框，表示被排除）
  clusters.forEach((cluster, clusterIndex) => {
    if (cluster === mainList) return

    cluster.elements.forEach((element, elementIndex) => {
      const htmlElement = element as HTMLElement
      htmlElement.setAttribute('data-post-highlight', 'excluded')
      htmlElement.style.border = '2px dashed #ef4444'
      htmlElement.style.boxShadow = '0 0 5px rgba(239, 68, 68, 0.2)'

      console.log(`[调试高亮] 排除聚类 ${clusterIndex + 1} 元素 ${elementIndex + 1} 已标记`)
    })
  })

  console.log(`[调试高亮] 高亮完成 - 主列表: ${mainList.elements.length} 个元素（绿色），排除: ${clusters.reduce((sum, c) => sum + (c === mainList ? 0 : c.elements.length), 0)} 个元素（红色虚线）`)
}

/**
 * 分析页面布局，输出详细的区域信息（调试功能）
 */
export function analyzePageLayout(): void {
  console.log('[页面布局分析] 开始分析页面布局...')

  const { clusters, mainList } = getAllClustersInfo()

  if (clusters.length === 0) {
    console.log('[页面布局分析] 未检测到任何聚类')
    return
  }

  console.group('[页面布局分析] 详细分析结果')

  clusters.forEach((cluster, index) => {
    console.group(`聚类 ${index + 1} ${cluster === mainList ? '(主列表)' : '(次要列表)'}`)

    console.log(`元素数量: ${cluster.elements.length}`)
    console.log(`置信度: ${cluster.confidence.toFixed(3)}`)

    // 分析位置分布
    const positions = cluster.elements.map(el => {
      const rect = el.getBoundingClientRect()
      return {
        left: rect.left,
        top: rect.top,
        width: rect.width,
        height: rect.height,
        centerX: rect.left + rect.width / 2,
        centerY: rect.top + rect.height / 2
      }
    })

    const avgCenterX = positions.reduce((sum, p) => sum + p.centerX, 0) / positions.length
    const avgCenterY = positions.reduce((sum, p) => sum + p.centerY, 0) / positions.length
    const avgWidth = positions.reduce((sum, p) => sum + p.width, 0) / positions.length

    console.log(`平均位置: X=${avgCenterX.toFixed(0)}, Y=${avgCenterY.toFixed(0)}`)
    console.log(`平均宽度: ${avgWidth.toFixed(0)}px`)
    console.log(`水平位置比例: ${(avgCenterX / window.innerWidth * 100).toFixed(1)}%`)

    // 分析区域类型
    const sampleElement = cluster.elements[0]
    const containerInfo = analyzeElementContainer(sampleElement)
    console.log(`容器分析:`, containerInfo)

    console.groupEnd()
  })

  console.groupEnd()
}

/**
 * 分析元素的容器信息
 */
function analyzeElementContainer(element: Element): {
  isInMain: boolean
  isInSidebar: boolean
  isInNav: boolean
  containerClasses: string[]
  containerTags: string[]
} {
  const containerClasses: string[] = []
  const containerTags: string[] = []

  let currentElement: Element | null = element
  let depth = 0

  while (currentElement && depth < 10) {
    if (currentElement.className) {
      containerClasses.push(...currentElement.className.split(' ').filter(c => c.trim()))
    }
    containerTags.push(currentElement.tagName.toLowerCase())
    currentElement = currentElement.parentElement
    depth++
  }

  return {
    isInMain: !!element.closest('main, [role="main"], .main, .main-content, #main'),
    isInSidebar: !!element.closest('aside, .sidebar, .side-bar, [role="complementary"]'),
    isInNav: !!element.closest('nav, .nav, .navigation, [role="navigation"]'),
    containerClasses: [...new Set(containerClasses)],
    containerTags: [...new Set(containerTags)]
  }
}

/**
 * 计算帖子信息的质量分数，用于去重时选择更好的版本
 */
function calculatePostQuality(post: PostInfo): number {
  let score = 0
  const reasons: string[] = []

  // 标题质量
  if (post.title && post.title !== '(无标题)') {
    score += 10
    reasons.push('有效标题')

    // 标题长度合理性
    if (post.title.length > 5 && post.title.length < 200) {
      score += 5
      reasons.push('标题长度合理')
    }
  }

  // 作者信息
  if (post.author && post.author !== '(无作者)') {
    score += 5
    reasons.push('有作者信息')
  }

  // 时间信息
  if (post.time && post.time !== '(无时间)') {
    score += 5
    reasons.push('有时间信息')
  }

  // 改进的链接质量评分
  if (post.link && post.link !== '(无链接)') {
    score += 10
    reasons.push('有链接')

    try {
      // 创建临时链接元素用于检测
      const tempLink = document.createElement('a')
      tempLink.href = post.link

      // 使用新的用户链接检测函数
      if (!isUserLink(post.link, tempLink)) {
        score += 15  // 提高非用户链接的权重
        reasons.push('非用户链接')
      } else {
        score -= 5   // 降低用户链接的分数
        reasons.push('用户链接(减分)')
      }

      // 内容链接特征加分
      const postUrlPatterns = [
        /\/(post|posts|article|articles|topic|topics|thread|threads)\/\d+/,
        /\/(story|stories|item|items|discussion|discussions)\/\d+/,
        /\/(question|questions|answer|answers)\/\d+/
      ]

      if (postUrlPatterns.some(pattern => pattern.test(post.link))) {
        score += 10
        reasons.push('内容URL模式')
      }

      // URL简洁性和可读性
      const url = new URL(post.link)
      const pathSegments = url.pathname.split('/').filter(seg => seg.length > 0)

      if (pathSegments.length <= 4) {
        score += 5  // 路径层级不太深
        reasons.push('路径简洁')
      }

      if (!url.search || url.search.length < 20) {
        score += 3  // 没有复杂的查询参数
        reasons.push('查询参数简单')
      }

      // 避免明显的操作链接
      const actionPatterns = [
        /\/edit/, /\/delete/, /\/reply/, /\/comment/,
        /\/share/, /\/like/, /\/vote/, /\/bookmark/
      ]

      if (actionPatterns.some(pattern => pattern.test(post.link))) {
        score -= 8
        reasons.push('操作链接(减分)')
      }

    } catch (error) {
      console.warn('[质量评分] URL解析失败:', post.link, error)
    }
  }

  // XPath简洁性（更短的XPath通常表示更直接的元素定位）
  if (post.xpath) {
    const pathLength = post.xpath.length
    if (pathLength < 100) {
      score += 5
      reasons.push('XPath简洁')
    }
    if (pathLength < 50) {
      score += 5
      reasons.push('XPath很简洁')
    }
  }

  console.log(`[质量评分] 帖子 "${post.title}" 评分: ${score} (${reasons.join(', ')})`)

  return score
}

/**
 * 通过XPath定位元素并高亮显示
 * @param xpath 元素的XPath路径
 * @returns 是否成功定位到元素
 */
export function navigateToElementByXPath(xpath: string): boolean {
  try {
    // 尝试使用XPath查找元素
    const result = document.evaluate(
      xpath,
      document,
      null,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null
    );
    
    const element = result.singleNodeValue as HTMLElement;
    
    // 如果找到元素
    if (element) {
      // 滚动到元素位置
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      
      // 临时高亮显示元素
      const originalOutline = element.style.outline;
      const originalBoxShadow = element.style.boxShadow;
      const originalPosition = element.style.position;
      const originalZIndex = element.style.zIndex;
      
      element.style.outline = '3px solid #4CAF50';
      element.style.boxShadow = '0 0 10px rgba(76, 175, 80, 0.7)';
      element.style.position = 'relative';
      element.style.zIndex = '1000';
      
      // 添加脉动动画效果
      const pulseAnimation = document.createElement('style');
      pulseAnimation.textContent = `
        @keyframes pulseHighlight {
          0% { outline-color: rgba(76, 175, 80, 1); box-shadow: 0 0 10px rgba(76, 175, 80, 0.7); }
          50% { outline-color: rgba(76, 175, 80, 0.5); box-shadow: 0 0 15px rgba(76, 175, 80, 0.9); }
          100% { outline-color: rgba(76, 175, 80, 1); box-shadow: 0 0 10px rgba(76, 175, 80, 0.7); }
        }
      `;
      document.head.appendChild(pulseAnimation);
      
      element.style.animation = 'pulseHighlight 1s infinite';
      
      // 3秒后恢复原样
      setTimeout(() => {
        element.style.outline = originalOutline;
        element.style.boxShadow = originalBoxShadow;
        element.style.position = originalPosition;
        element.style.zIndex = originalZIndex;
        element.style.animation = '';
        document.head.removeChild(pulseAnimation);
      }, 3000);
      
      console.log('[帖子导航] 成功定位到元素:', element);
      return true;
    } else {
      console.warn('[帖子导航] 未找到匹配的元素:', xpath);
      return false;
    }
  } catch (error) {
    console.error('[帖子导航] 定位元素时出错:', error);
    return false;
  }
}

/**
 * 获取主列表识别的详细报告
 */
export function getMainListReport(): {
  success: boolean
  mainListFound: boolean
  totalClusters: number
  mainListSize: number
  mainListConfidence: number
  excludedClusters: Array<{
    size: number
    confidence: number
    reason: string
  }>
  recommendations: string[]
} {
  console.log('[主列表报告] 生成主列表识别报告...')

  const { clusters, mainList } = getAllClustersInfo()

  const report = {
    success: clusters.length > 0,
    mainListFound: !!mainList,
    totalClusters: clusters.length,
    mainListSize: mainList ? mainList.elements.length : 0,
    mainListConfidence: mainList ? mainList.confidence : 0,
    excludedClusters: [] as Array<{
      size: number
      confidence: number
      reason: string
    }>,
    recommendations: [] as string[]
  }

  // 分析被排除的聚类
  clusters.forEach(cluster => {
    if (cluster !== mainList) {
      let reason = '评分较低'

      if (cluster.elements.length < 3) {
        reason = '元素数量太少'
      } else if (cluster.confidence < 0.3) {
        reason = '置信度太低'
      } else {
        // 简单的位置分析
        const avgX = cluster.elements.reduce((sum, el) => {
          const rect = el.getBoundingClientRect()
          return sum + (rect.left + rect.width / 2)
        }, 0) / cluster.elements.length

        const horizontalRatio = avgX / window.innerWidth

        if (horizontalRatio < 0.2 || horizontalRatio > 0.8) {
          reason = '位置偏向边缘，可能是侧边栏'
        }
      }

      report.excludedClusters.push({
        size: cluster.elements.length,
        confidence: cluster.confidence,
        reason
      })
    }
  })

  // 生成建议
  if (!report.mainListFound) {
    report.recommendations.push('未找到主列表，建议检查页面是否包含帖子列表结构')
    report.recommendations.push('可以尝试调整聚类参数或检查页面DOM结构')
  } else if (report.mainListSize < 5) {
    report.recommendations.push('主列表元素较少，可能需要调整检测参数')
  } else if (report.mainListConfidence < 0.5) {
    report.recommendations.push('主列表置信度较低，建议验证检测结果')
  }

  if (report.excludedClusters.length > 0) {
    report.recommendations.push(`成功排除了 ${report.excludedClusters.length} 个次要列表，避免了数据混淆`)
  }

  console.log('[主列表报告] 报告生成完成:', report)
  return report
}

/**
 * 一键诊断页面帖子检测情况
 */
export function diagnosePostDetection(): void {
  console.group('🔍 帖子检测诊断报告')

  console.log('📊 开始全面诊断页面帖子检测情况...')

  // 1. 基础检测
  console.group('1️⃣ 基础检测')
  const report = getMainListReport()
  console.log(`✅ 检测成功: ${report.success}`)
  console.log(`🎯 主列表识别: ${report.mainListFound}`)
  console.log(`📝 总聚类数: ${report.totalClusters}`)
  console.log(`📏 主列表大小: ${report.mainListSize}`)
  console.log(`🎲 主列表置信度: ${report.mainListConfidence.toFixed(3)}`)
  console.groupEnd()

  // 2. 排除分析
  if (report.excludedClusters.length > 0) {
    console.group('2️⃣ 排除的次要列表')
    report.excludedClusters.forEach((excluded, index) => {
      console.log(`❌ 聚类 ${index + 1}: ${excluded.size} 个元素, 置信度 ${excluded.confidence.toFixed(3)} - ${excluded.reason}`)
    })
    console.groupEnd()
  }

  // 3. 建议
  if (report.recommendations.length > 0) {
    console.group('3️⃣ 建议和说明')
    report.recommendations.forEach((rec, index) => {
      console.log(`💡 ${index + 1}. ${rec}`)
    })
    console.groupEnd()
  }

  // 4. 页面布局分析
  console.group('4️⃣ 页面布局分析')
  analyzePageLayout()
  console.groupEnd()

  // 5. 最终数据
  console.group('5️⃣ 最终提取的帖子数据')
  const posts = getPostsAsJson()
  console.log(`📋 最终帖子数量: ${posts.length}`)
  if (posts.length > 0) {
    console.log('📄 帖子列表预览:')
    posts.slice(0, 3).forEach((post, index) => {
      console.log(`  ${index + 1}. ${post.title}`)
    })
    if (posts.length > 3) {
      console.log(`  ... 还有 ${posts.length - 3} 个帖子`)
    }
  }
  console.groupEnd()

  console.log('🎉 诊断完成！')
  console.groupEnd()
}