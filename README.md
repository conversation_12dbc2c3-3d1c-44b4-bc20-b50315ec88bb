## 功能介绍

这是一个浏览器扩展项目，目前包含以下功能：

### 📊 关键词词云显示
- 在网页右上角显示可展开/收起的词云面板
- 根据关键词热度（关联文章数量）动态调整字体大小
- 支持点击词云中的关键词查看详细信息
- 详情面板显示关键词相关的文章列表
- 支持固定查看特定关键词，便于深入了解
- 词云采用原生Canvas渲染，性能优异
- 使用示例数据展示热门关键词分布

## 技术栈

- [Plasmo](https://docs.plasmo.com/) - 浏览器扩展开发框架
- [React](https://reactjs.org/) - 用户界面库
- [TypeScript](https://www.typescriptlang.org/) - 类型安全的JavaScript超集
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的CSS框架
- [Shadcn UI](https://ui.shadcn.com/) - 基于Radix UI和Tailwind CSS的组件库
- [Lucide React](https://lucide.dev/) - 图标库
- [OpenAI API](https://openai.com/) - AI大模型服务，支持流式输出
- [jsonrepair](https://github.com/josdejong/jsonrepair) - JSON修复库，处理不完整的JSON数据
- 原生Canvas - 用于词云渲染的原生实现

## 编码规范

### 文件命名规范
* 目录名使用小写，多个单词用`-`连接
* 文件名采用小驼峰，如`readLater.ts`
* 组件文件名采用大驼峰，如`ReadLater.tsx`。
    * plasmo框架文件除外（不使用大驼峰），包括`popup.tsx`，`options.tsx`
    * shadcn组件除外（不使用大驼峰），（即`components/ui`目录内的）

### shadcn规范
* 添加shadcn组件方式：`pnpm dlx shadcn@2.3.0 add [组件名]`

## 目录结构

```
├── .cursor/               # Cursor IDE配置文件
│   └── rules/             # 代码规则配置
├── .editorconfig          # 编辑器配置
├── .github/               # GitHub相关配置文件
│   └── workflows/         # GitHub Actions工作流
├── .gitignore             # Git忽略文件配置
├── .prettierrc.mjs        # Prettier代码格式化配置
├── .trae/                 # Trae AI配置文件
│   └── rules/             # 项目规则配置
├── .vercel/               # Vercel部署配置
├── README.md              # 项目说明文档
├── assets/                # 静态资源文件（图标、图片等）
│   ├── head.png           # 头像图标
│   └── icon.png           # 扩展图标
├── background/            # 扩展的后台脚本
│   ├── index.ts           # 后台脚本入口文件
│   └── messages/          # 消息通信处理相关脚本
│       └── ping.ts        # ping消息处理器
├── components.json        # Shadcn UI组件配置
├── components/            # React组件
│   ├── popup/             # 弹出窗口组件
│   │   └── Common.tsx     # 通用弹出窗口组件
│   ├── ui/                # Shadcn UI组件
│   │   ├── button.tsx     # 按钮组件
│   │   ├── card.tsx       # 卡片组件
│   │   ├── checkbox.tsx   # 复选框组件
│   │   ├── input.tsx      # 输入框组件
│   │   ├── label.tsx      # 标签组件
│   │   ├── theme-provider.tsx # 主题提供者组件
│   │   └── theme-toggle.tsx   # 主题切换组件
│   └── wordcloud/         # 词云相关组件
│       ├── DetailPanel.tsx    # 词云详情面板组件
│       ├── WordCloud.tsx      # 词云渲染组件
│       └── WordCloudPanel.tsx # 词云主面板组件
├── contents/              # 内容脚本目录
│   ├── Demo.tsx           # 示例内容脚本
│   └── WordCloudContent.tsx # 词云内容脚本
├── hooks/                 # 自定义React钩子（空目录）
├── lib/                   # 工具库和辅助函数
│   ├── utils.ts           # 通用工具函数
│   └── wordcloudUtils.ts  # 词云数据处理工具
├── options.tsx            # 扩展选项页面
├── package.json           # 项目依赖和脚本
├── pnpm-lock.yaml         # pnpm锁定文件
├── popup.tsx              # 扩展弹出窗口页面
├── postcss.config.js      # PostCSS配置
├── sidepanel.tsx          # 侧边栏面板页面
├── store/                 # Redux状态管理
│   └── store.ts           # 状态存储配置
├── style.css              # 全局样式
├── tabs/                  # 扩展标签页（空目录）
├── tailwind.config.js     # Tailwind CSS配置
├── tsconfig.json          # TypeScript配置
└── typings.d.ts           # TypeScript类型定义
```

## 添加功能示例
比如添加一个`todo list`功能。

1. 在`background`目录里添加`todoList.ts`文件，里面添加相关后台脚本逻辑，并相应修改`background/index.ts`文件。
    1. 如果涉及内容页面/扩展页面跟后台脚本的消息通信，则在`background/messages`里新建通信处理器（`PlasmoMessaging.MessageHandler`实例），用来处理发送给后台的消息。
2. 在`contents`目录里添加`TodoList.tsx`文件，里面添加内容脚本组件代码(请查看示例`contents/Demo.tsx`)。
3. 在`store`目录里添加`todoListSlice.ts`文件，里面添加redux状态管理相关代码，然后修改`store/store.ts`文件添加对应slice。
4. 在`components`目录里添加`todo-list`子目录，里面添加拆分后的模块化组件。
5. 如果涉及tab页面，则在`tabs`目录里添加`TodoList.tsx`，作为待办列表的管理页面。
    1. tab页面的访问方式：`chrome-extension://<extension-id>/tabs/TodoList.html`，或者`chrome.runtime.getURL("tabs/TodoList.html")`
    2. 注意！tab页面会包含`<Provider>`组件，因此页面里不能直接使用`useAppSelector`等，而要放入子组件中，否则会出现找不到react-redux上下文的错误！
6. 如果涉及弹出窗口，则`popup.tsx`文件也要修改。
7. 如果涉及配置选项，则`options.tsx`文件也要改。
8. 这是浏览器扩展项目，通信使用plasmo框架的通信方式，样式使用tailwindcss，基础组件库使用shadcn，图标库使用lucide-react，通用的方法放到`lib`目录下，自定义react hook放到`hooks`目录下。

## 开始使用

首先，运行开发服务器：

```bash
pnpm dev
```

打开您的浏览器并加载适当的开发构建版本。例如，如果您正在为 Chrome 浏览器开发，使用 manifest v3，请使用：`build/chrome-mv3-dev`。

## 安装扩展

1. 在Chrome浏览器中，打开扩展管理页面 (chrome://extensions/)
2. 开启"开发者模式"
3. 点击"加载已解压的扩展"
4. 选择项目的 `build/chrome-mv3-dev` 目录

## 生产构建

运行以下命令：

```bash
pnpm build
```

这将为您的扩展创建一个生产捆绑包，可以打包并发布到各应用商店。

## 提交到网上应用商店

部署 Plasmo 扩展的最简单方法是使用内置的 [bpp](https://bpp.browser.market) GitHub action。但在使用此操作之前，请确保构建您的扩展并将第一个版本上传到商店以建立基本凭据。然后，只需按照[此设置说明](https://docs.plasmo.com/framework/workflows/submit)操作，您就可以实现自动提交了！

## 📊 词云功能

本扩展的核心功能是基于网页内容生成关键词词云，并提供智能的帖子列表检测功能。

### 🎯 主要特性

1. **面板控制**：
   - 右上角固定显示词云面板
   - **点击面板标题栏**可以展开/收起词云内容
2. **帖子列表检测**：
   - **展开词云面板时自动触发**帖子列表检测
   - 使用结构聚类算法分析网页DOM结构
   - 检测过程和结果会输出到浏览器控制台
   - **双击"关键词词云"标题**可启用/关闭调试高亮模式🔍
3. **AI智能分析**：
   - 🚀 **流式输出**：使用OpenAI流式API，实时生成关键词
   - 🔧 **JSON修复**：自动修复AI返回的不完整JSON数据
   - ⚡ **实时更新**：每收到完整的一行数据就更新词云，减少等待时间
   - 🎯 **智能去重**：避免重复更新相同的词云数据
4. **词云显示**：
   - 字体大小反映关键词热度（关联文章数量越多，字体越大）
   - 显示前15个最热门的关键词
   - 词云采用随机颜色和旋转角度，视觉效果丰富
   - **流畅更新**：支持实时更新，减少界面抖动
5. **交互功能**：
   - **悬停关键词**：鼠标悬停在词云中的关键词上时，右侧会显示详情面板（虚线边框）
   - **点击关键词**：点击关键词可固定显示详情面板（实线边框），方便查看相关文章
   - **切换关键词**：在固定状态下点击其他关键词可直接切换到新词的详情
   - **关闭详情**：点击详情面板右上角的关闭按钮或点击同一关键词可取消固定显示
6. **详情面板**：
   - 显示关键词相关的文章标题列表
   - 点击文章标题可进行相应操作（当前为控制台输出）
   - 边框样式区分状态：虚线表示悬停，实线表示固定

### 🔍 帖子检测功能详解

#### 检测原理
扩展使用**结构聚类算法 + 主列表识别**来检测网页中的帖子列表：

1. **结构签名提取**：对每个DOM元素提取以下特征
   - 标签名称序列（如 `div>h2>a>span`）
   - CSS类名
   - 子元素数量
   - 文本长度、链接数量、图片数量

2. **相似度计算**：通过加权计算不同特征的相似度
   - 标签序列相似度（权重25%）
   - 标签名相似度（权重30%）
   - 类名相似度（权重20%）
   - 其他结构特征（权重25%）

3. **聚类分析**：将相似度超过阈值的元素归为一组
   - 最小聚类大小：3个元素
   - 相似度阈值：0.6
   - 按置信度排序输出结果

4. **🎯 主列表识别**（新增）：智能识别真正的主帖子列表
   - **单一列表原则**：只从一个主列表中提取数据，避免混淆
   - **多因素评分**：位置(25%) + 大小(30%) + 置信度(20%) + 内容质量(15%) + 区域类型(10%)
   - **自动排除**：侧边栏、推荐区域、导航菜单等次要列表
   - **智能判断**：基于页面布局和元素特征识别主内容区域

#### 使用方法

1. **基本检测**：
   - 打开任意网页（如论坛、新闻站点、博客等）
   - 点击词云面板标题栏展开面板
   - 查看浏览器控制台输出的检测日志

2. **调试模式**：
   - 双击"关键词词云"标题启用调试高亮模式🔍
   - 再次展开面板时，检测到的帖子会用彩色边框高亮显示
   - **绿色边框**：主列表元素（最终提取的数据来源）
   - **红色虚线**：被排除的次要列表元素

3. **🔧 高级调试功能**：
   ```javascript
   // 在浏览器控制台中使用以下命令进行深度分析

   // 一键诊断页面帖子检测情况
   diagnosePostDetection()

   // 高亮显示主列表
   highlightMainList()

   // 获取主列表识别报告
   getMainListReport()

   // 分析页面布局
   analyzePageLayout()

   // 获取所有聚类信息
   getAllClustersInfo()
   ```

#### 🎯 主列表识别详解

**问题背景**：
网页中通常包含多个帖子列表区域，例如：
- 主要帖子列表（页面核心内容）
- 侧边栏热门帖子列表
- 推荐帖子列表
- 相关帖子列表

**解决方案**：
本扩展实现了智能主列表识别机制，确保数据提取的准确性：

1. **单一列表原则**：
   - ✅ 只从一个真正的主帖子列表中提取数据
   - ❌ 不会将多个不同格式的列表合并
   - ✅ 避免侧边栏、推荐区域等次要列表的干扰

2. **智能评分算法**：
   - **位置评分(25%)**：主列表通常位于页面中心区域
   - **大小评分(30%)**：主列表包含更多帖子元素
   - **置信度评分(20%)**：基于结构相似度的聚类置信度
   - **内容质量评分(15%)**：标题、链接等信息的完整性
   - **区域类型评分(10%)**：基于CSS类名和HTML结构判断

3. **自动排除机制**：
   - 🚫 侧边栏区域 (`aside`, `.sidebar`, `[role="complementary"]`)
   - 🚫 导航菜单 (`nav`, `.navigation`, `[role="navigation"]`)
   - 🚫 推荐/热门区域 (`.recommended`, `.popular`, `.trending`)
   - 🚫 位置偏向边缘的列表（可能是侧边栏）

4. **质量保证**：
   - 📊 详细的识别过程日志
   - 🔍 可视化高亮显示（调试模式）
   - 📋 完整的识别报告和建议
   - ⚠️ 识别异常时的警告提示

3. **控制台输出示例**：
   ```
   [主列表识别] 开始分析 3 个聚类，识别主要帖子列表
   [主列表识别] 聚类 1 评分: 67.5 (聚类大小: 8个元素 (+24.0), 置信度: 0.800 (+16.0), 位置评分: +18.2, 内容质量: +9.3, 区域类型: +0.0)
   [主列表识别] 聚类 2 评分: 23.1 (聚类大小: 3个元素 (+9.0), 置信度: 0.600 (+12.0), 位置评分: +2.1, 内容质量: +0.0, 区域类型: +0.0)
   [主列表识别] 聚类 3 评分: 15.8 (聚类大小: 5个元素 (+15.0), 置信度: 0.400 (+8.0), 位置评分: +2.8, 内容质量: +0.0, 区域类型: -10.0)
   [主列表识别] 选择聚类作为主列表，得分: 67.5
   [帖子提取] 从主列表提取帖子数据，包含 8 个元素
   [帖子提取] 数据来源: 单一主列表 (避免了侧边栏、推荐区域等次要列表的干扰)
   ```

#### JSON字段说明

检测到的每个帖子都会转换为包含以下字段的JSON对象：

| 字段 | 说明 | 示例 |
|------|------|------|
| `title` | 帖子标题 | "如何使用结构聚类检测帖子列表" |
| `xpath` | 元素的XPath路径 | "/html/body/div[1]/main/article[1]" |
| `author` | 作者/发布者 | "张三" |
| `time` | 发布时间 | "2024-01-15" 或 "2小时前" |
| `link` | 帖子链接 | "https://example.com/post/123" |

**字段提取策略**：
- **标题**：优先查找h1-h6标签，然后查找包含"title"类名的元素
- **作者**：查找包含"author"、"user"类名的元素或用户链接
- **时间**：查找time标签、datetime属性，或匹配时间格式的文本
- **链接**：查找第一个有效的a标签href属性
- **XPath**：生成元素的完整路径，优先使用ID定位

#### 适用场景
- 论坛帖子列表（如知乎、Reddit、贴吧）
- 新闻网站文章列表
- 博客文章列表
- 电商商品列表
- 社交媒体动态列表

### 🔧 开发说明

- 词云组件位于 `components/wordcloud/` 目录：
  - `WordCloudPanel.tsx` - 主面板组件，管理整体状态和布局，支持流式更新
  - `WordCloud.tsx` - 词云渲染组件，基于Canvas实现
  - `DetailPanel.tsx` - 详情面板组件，显示关键词相关信息
- AI服务层位于 `lib/` 目录：
  - `openaiService.ts` - OpenAI服务封装，支持流式输出和JSON修复
  - `wordcloudProcessor.ts` - 词云数据处理器，集成流式API调用
  - `wordcloudUtils.ts` - 词云数据转换工具
- 帖子检测逻辑在 `lib/postDetection.ts` 中
- 内容脚本 `contents/WordCloudContent.tsx` 负责将面板注入到网页
- 状态管理使用React hooks，支持悬停和固定两种交互模式
- 使用示例在 `examples/streamingExample.ts` 中

#### 🚀 流式输出特性

1. **实时响应**：AI生成过程中实时更新词云，无需等待完整响应
2. **JSON修复**：自动处理AI返回的不完整JSON数据，提高解析成功率
3. **按行更新**：每收到完整的一行数据就尝试解析和更新，减少界面抖动
4. **错误恢复**：即使中间解析失败，也会继续尝试，直到获得有效数据
5. **性能优化**：避免重复更新相同的数据，减少不必要的渲染
